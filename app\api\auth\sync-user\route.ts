// app/api/auth/sync-user/route.ts
import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  try {
    const { supabaseId, email } = await req.json();

    if (!supabaseId || !email) {
      return NextResponse.json(
        { error: "Missing supabaseId or email" },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { supabaseId },
    });

    if (existingUser) {
      return NextResponse.json({ user: existingUser });
    }

    // Create new user with default USER role
    const newUser = await prisma.user.create({
      data: {
        supabaseId,
        email,
        role: "USER", // Default role
      },
    });

    return NextResponse.json({ user: newUser });
  } catch (error) {
    console.error("Error syncing user:", error);
    return NextResponse.json(
      { error: "Failed to sync user" },
      { status: 500 }
    );
  }
}
