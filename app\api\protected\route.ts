// app/api/protected/route.ts
import { supabase } from "@/lib/supabase";

export async function GET(req: Request) {
  const accessToken = req.headers.get("Authorization")?.replace("Bearer ", "");
  if (!accessToken) return new Response("Unauthorized", { status: 401 });

  const { data: { user }, error } = await supabase.auth.getUser(accessToken);
  if (error || !user) return new Response("Unauthorized", { status: 401 });

  // ✅ User is authenticated
  return new Response(JSON.stringify({ message: "Protected content", user }));
}
